using UnityEngine;
using UnityEngine.UIElements;

[System.Serializable]
public class QuickFixer : MonoBeh<PERSON>our
{
    [Head<PERSON>("🔧 Quick Fix untuk Masalah Anda")]
    [TextArea(5, 10)]
    public string instructions = @"
MASALAH YANG TERDETEKSI:
1. No story data assigned
2. UIDocument component missing

SOLUSI:
Klik tombol 'Fix Now' di bawah atau jalankan scene.
Script ini akan otomatis memperbaiki semua masalah!
";

    [Header("Auto Fix")]
    public bool autoFixOnStart = true;

    private void Start()
    {
        if (autoFixOnStart)
        {
            FixAllProblems();
        }
    }

    [ContextMenu("Fix Now - Perbaiki Sekarang")]
    public void FixAllProblems()
    {
        Debug.Log("🔧 MEMULAI PERBAIKAN OTOMATIS...");

        // Fix 1: Tambahkan UIDocument component
        FixUIDocumentProblem();

        // Fix 2: Assign story data
        FixStoryDataProblem();

        // Fix 3: Pastikan semua komponen ada
        EnsureAllComponents();

        // Fix 4: Perbaiki Input System conflicts
        FixInputSystemConflicts();

        // Fix 5: Perbaiki missing script references
        FixMissingScriptReferences();

        Debug.Log("✅ SEMUA MASALAH TELAH DIPERBAIKI!");
        Debug.Log("🎮 Sekarang coba jalankan scene lagi!");
    }

    private void FixUIDocumentProblem()
    {
        Debug.Log("🎨 Memperbaiki masalah UIDocument...");
        
        // Cek apakah UIDocument sudah ada
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            // Tambahkan UIDocument component
            uiDocument = gameObject.AddComponent<UIDocument>();
            Debug.Log("✅ UIDocument component berhasil ditambahkan!");
        }
        
        // Assign StoryPage.uxml
        if (uiDocument.visualTreeAsset == null)
        {
            // Coba load dari Resources
            var uxml = Resources.Load<VisualTreeAsset>("StoryPage");
            if (uxml == null)
            {
                // Coba dari UI folder
                uxml = Resources.Load<VisualTreeAsset>("UI/StoryPage");
            }
            
            if (uxml != null)
            {
                uiDocument.visualTreeAsset = uxml;
                Debug.Log("✅ StoryPage.uxml berhasil di-assign!");
            }
            else
            {
                Debug.LogWarning("⚠️ StoryPage.uxml tidak ditemukan di Resources. Akan menggunakan fallback...");
                // Buat UXML sederhana sebagai fallback
                CreateFallbackUXML(uiDocument);
            }
        }
    }

    private void FixStoryDataProblem()
    {
        Debug.Log("📝 Memperbaiki masalah Story Data...");
        
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null)
        {
            dialogueSystem = gameObject.AddComponent<DialogueSystem>();
            Debug.Log("✅ DialogueSystem component berhasil ditambahkan!");
        }
        
        // Cek apakah dialogue data sudah ada
        if (dialogueSystem.dialogues == null || dialogueSystem.dialogues.Length == 0)
        {
            Debug.Log("📝 Mencari story data...");

            // Coba berbagai lokasi untuk MaimyStoryData
            SampleStoryData storyData = null;

            // Coba dari Resources folder
            storyData = Resources.Load<SampleStoryData>("MaimyStoryData");
            if (storyData == null)
            {
                Debug.Log("🔍 Mencoba lokasi lain...");
                // Coba nama lain
                storyData = Resources.Load<SampleStoryData>("SampleStoryData");
            }

            if (storyData != null && storyData.dialogues != null && storyData.dialogues.Length > 0)
            {
                dialogueSystem.dialogues = storyData.dialogues;
                Debug.Log($"✅ Story data berhasil di-load! {storyData.dialogues.Length} dialogues ditemukan");
            }
            else
            {
                Debug.LogWarning("⚠️ Story data tidak ditemukan, membuat data sederhana...");
                // Buat story data sederhana
                CreateSimpleStoryData(dialogueSystem);
            }
        }
        else
        {
            Debug.Log($"✅ Story data sudah ada: {dialogueSystem.dialogues.Length} dialogues");
        }
    }

    private void CreateSimpleStoryData(DialogueSystem dialogueSystem)
    {
        Debug.Log("📖 Membuat story data sederhana...");
        
        var simpleDialogues = new DialogueData[]
        {
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Halo! Aku Maimy, AI companion kamu. Senang bertemu denganmu!",
                backgroundImage = "MaimyBackground",
                characterImage = "maimysenyummanis",
                characterPosition = "right",
                autoDelay = 3f
            },
            new DialogueData
            {
                characterName = "Maimy", 
                dialogueText = "Sekarang visual novel sudah berjalan dengan baik. Kamu bisa menambahkan lebih banyak dialog nanti!",
                backgroundImage = "MaimyBackground",
                characterImage = "maimycuriouscute",
                characterPosition = "right",
                autoDelay = 4f
            },
            new DialogueData
            {
                characterName = "System",
                dialogueText = "Perbaikan berhasil! Sekarang kamu bisa menambahkan story data sendiri atau menggunakan MaimyStoryData.asset",
                backgroundImage = "",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 5f
            }
        };
        
        dialogueSystem.dialogues = simpleDialogues;
        Debug.Log("✅ Story data sederhana berhasil dibuat!");
    }

    private void CreateFallbackUXML(UIDocument uiDocument)
    {
        Debug.Log("🎨 Membuat UXML fallback...");
        
        // Untuk sementara, biarkan null dan beri pesan
        Debug.LogWarning("⚠️ Silakan assign UI/StoryPage.uxml secara manual ke UIDocument component");
        Debug.LogWarning("💡 Atau copy StoryPage.uxml ke folder Resources/");
    }

    private void EnsureAllComponents()
    {
        Debug.Log("🔧 Memastikan semua komponen ada...");
        
        // DialogueSystem
        if (GetComponent<DialogueSystem>() == null)
        {
            gameObject.AddComponent<DialogueSystem>();
            Debug.Log("✅ DialogueSystem ditambahkan");
        }
        
        // StorySceneController
        if (GetComponent<StorySceneController>() == null)
        {
            gameObject.AddComponent<StorySceneController>();
            Debug.Log("✅ StorySceneController ditambahkan");
        }
        
        // CinematicEffects (opsional)
        if (GetComponent<CinematicEffects>() == null)
        {
            gameObject.AddComponent<CinematicEffects>();
            Debug.Log("✅ CinematicEffects ditambahkan");
        }
        
        // DialogueSystemFixer untuk keamanan ekstra
        if (GetComponent<DialogueSystemFixer>() == null)
        {
            gameObject.AddComponent<DialogueSystemFixer>();
            Debug.Log("✅ DialogueSystemFixer ditambahkan untuk keamanan ekstra");
        }
    }

    [ContextMenu("Test Story Data")]
    public void TestStoryData()
    {
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem != null && dialogueSystem.dialogues != null)
        {
            Debug.Log($"✅ Story data OK: {dialogueSystem.dialogues.Length} dialogues tersedia");
            for (int i = 0; i < dialogueSystem.dialogues.Length; i++)
            {
                var dialogue = dialogueSystem.dialogues[i];
                Debug.Log($"  {i+1}. {dialogue.characterName}: {dialogue.dialogueText.Substring(0, Mathf.Min(50, dialogue.dialogueText.Length))}...");
            }
        }
        else
        {
            Debug.LogError("❌ Story data masih bermasalah!");
        }
    }

    [ContextMenu("Test UIDocument")]
    public void TestUIDocument()
    {
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument != null)
        {
            if (uiDocument.visualTreeAsset != null)
            {
                Debug.Log("✅ UIDocument OK: " + uiDocument.visualTreeAsset.name);
            }
            else
            {
                Debug.LogError("❌ UIDocument ada tapi visualTreeAsset null!");
            }
        }
        else
        {
            Debug.LogError("❌ UIDocument component tidak ada!");
        }
    }

    private void FixInputSystemConflicts()
    {
        Debug.Log("🎮 Memperbaiki konflik Input System...");

        // Tambahkan InputSystemFixer untuk mengatasi konflik
        if (GetComponent<InputSystemFixer>() == null)
        {
            gameObject.AddComponent<InputSystemFixer>();
            Debug.Log("✅ InputSystemFixer ditambahkan untuk mengatasi konflik input!");
        }

        // Disable StorySceneController sementara untuk mencegah konflik
        var oldController = GetComponent<StorySceneController>();
        if (oldController != null)
        {
            oldController.enabled = false;
            Debug.Log("✅ StorySceneController di-disable untuk mencegah konflik input");
            Debug.Log("💡 Gunakan mouse click pada tombol Next atau InputSystemFixer untuk kontrol");
        }
    }

    private void FixMissingScriptReferences()
    {
        Debug.Log("🔗 Memperbaiki missing script references...");

        // Cek dan perbaiki komponen yang rusak
        var components = GetComponents<MonoBehaviour>();
        int missingCount = 0;

        foreach (var component in components)
        {
            if (component == null)
            {
                missingCount++;
            }
        }

        if (missingCount > 0)
        {
            Debug.LogWarning($"⚠️ Ditemukan {missingCount} missing script references");
            Debug.Log("💡 Silakan hapus komponen yang rusak secara manual di Inspector");
        }
        else
        {
            Debug.Log("✅ Tidak ada missing script references");
        }
    }
}
