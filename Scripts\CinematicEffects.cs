using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;

public class CinematicEffects : MonoBehaviour
{
    [Header("Fade Settings")]
    public Color fadeColor = Color.black;
    public AnimationCurve fadeCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    [Header("Parallax Settings")]
    public bool enableParallax = true;
    public float parallaxStrength = 0.02f;
    public float parallaxSmoothness = 2f;

    [Header("Camera Shake Settings")]
    public float shakeIntensity = 0.1f;
    public float shakeDuration = 0.5f;

    [Header("Lighting Effects")]
    public bool enableLightingOverlay = true;
    public Gradient lightingGradient;
    public float lightingCycleSpeed = 1f;

    private UIDocument uiDocument;
    private VisualElement fadeOverlay;
    private VisualElement backgroundImage;
    private VisualElement lightingOverlay;
    private Camera mainCamera;
    private Vector3 originalCameraPosition;
    private Vector2 lastMousePosition;

    // Coroutines
    private Coroutine fadeCoroutine;
    private Coroutine shakeCoroutine;
    private Coroutine lightingCoroutine;

    private void OnEnable()
    {
        InitializeEffects();
        SetupParallax();
        SetupLighting();
    }

    private void InitializeEffects()
    {
        uiDocument = GetComponent<UIDocument>();
        mainCamera = Camera.main;
        
        if (mainCamera != null)
        {
            originalCameraPosition = mainCamera.transform.position;
        }

        CreateFadeOverlay();
        CreateLightingOverlay();
        
        var root = uiDocument.rootVisualElement;
        backgroundImage = root.Q("background-image");
    }

    private void CreateFadeOverlay()
    {
        if (uiDocument != null)
        {
            var root = uiDocument.rootVisualElement;
            
            fadeOverlay = new VisualElement();
            fadeOverlay.name = "fade-overlay";
            fadeOverlay.style.position = Position.Absolute;
            fadeOverlay.style.width = new StyleLength(new Length(100, LengthUnit.Percent));
            fadeOverlay.style.height = new StyleLength(new Length(100, LengthUnit.Percent));
            fadeOverlay.style.backgroundColor = fadeColor;
            fadeOverlay.style.opacity = 0f;
            fadeOverlay.pickingMode = PickingMode.Ignore;
            
            root.Add(fadeOverlay);
        }
    }

    private void CreateLightingOverlay()
    {
        if (uiDocument != null && enableLightingOverlay)
        {
            var root = uiDocument.rootVisualElement;
            
            lightingOverlay = new VisualElement();
            lightingOverlay.name = "lighting-overlay";
            lightingOverlay.style.position = Position.Absolute;
            lightingOverlay.style.width = new StyleLength(new Length(100, LengthUnit.Percent));
            lightingOverlay.style.height = new StyleLength(new Length(100, LengthUnit.Percent));
            lightingOverlay.style.opacity = 0.3f;
            lightingOverlay.pickingMode = PickingMode.Ignore;
            
            // Insert after background but before other elements
            if (backgroundImage != null)
            {
                root.Insert(1, lightingOverlay);
            }
            else
            {
                root.Insert(0, lightingOverlay);
            }
            
            StartLightingCycle();
        }
    }

    private void SetupParallax()
    {
        if (enableParallax)
        {
            lastMousePosition = Input.mousePosition;
        }
    }

    private void SetupLighting()
    {
        if (lightingGradient == null)
        {
            lightingGradient = new Gradient();
            var colorKeys = new GradientColorKey[]
            {
                new GradientColorKey(new Color(1f, 0.8f, 0.6f, 0.2f), 0f),
                new GradientColorKey(new Color(0.6f, 0.8f, 1f, 0.3f), 0.5f),
                new GradientColorKey(new Color(1f, 0.7f, 0.9f, 0.25f), 1f)
            };
            var alphaKeys = new GradientAlphaKey[]
            {
                new GradientAlphaKey(0.2f, 0f),
                new GradientAlphaKey(0.3f, 0.5f),
                new GradientAlphaKey(0.2f, 1f)
            };
            lightingGradient.SetKeys(colorKeys, alphaKeys);
        }
    }

    private void Update()
    {
        if (enableParallax)
        {
            UpdateParallax();
        }
    }

    private void UpdateParallax()
    {
        Vector2 currentMousePosition = Input.mousePosition;
        Vector2 mouseDelta = (currentMousePosition - lastMousePosition) * parallaxStrength;
        
        if (backgroundImage != null)
        {
            // Get current translate values or use zero if not set
            var currentTranslate = backgroundImage.style.translate.value;
            float currentX = currentTranslate.x.value;
            float currentY = currentTranslate.y.value;
            
            // Calculate new position
            float newX = currentX + mouseDelta.x;
            float newY = currentY + mouseDelta.y;
            
            // Apply parallax with smoothing
            float smoothX = Mathf.Lerp(currentX, newX, Time.deltaTime * parallaxSmoothness);
            float smoothY = Mathf.Lerp(currentY, newY, Time.deltaTime * parallaxSmoothness);
            
            backgroundImage.style.translate = new StyleTranslate(new Translate(smoothX, smoothY));
        }
        
        lastMousePosition = Vector2.Lerp(lastMousePosition, currentMousePosition, 
            Time.deltaTime * parallaxSmoothness);
    }

    // Fade Effects
    public void FadeIn(float duration = 1f)
    {
        if (fadeCoroutine != null)
        {
            StopCoroutine(fadeCoroutine);
        }
        fadeCoroutine = StartCoroutine(FadeCoroutine(1f, 0f, duration));
    }

    public void FadeOut(float duration = 1f)
    {
        if (fadeCoroutine != null)
        {
            StopCoroutine(fadeCoroutine);
        }
        fadeCoroutine = StartCoroutine(FadeCoroutine(0f, 1f, duration));
    }

    private IEnumerator FadeCoroutine(float startAlpha, float endAlpha, float duration)
    {
        if (fadeOverlay == null) yield break;

        float elapsedTime = 0f;
        
        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / duration;
            float curveValue = fadeCurve.Evaluate(progress);
            float alpha = Mathf.Lerp(startAlpha, endAlpha, curveValue);
            
            fadeOverlay.style.opacity = alpha;
            yield return null;
        }
        
        fadeOverlay.style.opacity = endAlpha;
    }

    // Camera Shake
    public void CameraShake(float intensity = -1f, float duration = -1f)
    {
        if (mainCamera == null) return;
        
        if (intensity < 0) intensity = shakeIntensity;
        if (duration < 0) duration = shakeDuration;
        
        if (shakeCoroutine != null)
        {
            StopCoroutine(shakeCoroutine);
        }
        shakeCoroutine = StartCoroutine(CameraShakeCoroutine(intensity, duration));
    }

    private IEnumerator CameraShakeCoroutine(float intensity, float duration)
    {
        float elapsedTime = 0f;
        
        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            
            Vector3 randomOffset = Random.insideUnitSphere * intensity;
            randomOffset.z = 0; // Keep camera on same Z plane
            
            float dampening = 1f - (elapsedTime / duration);
            mainCamera.transform.position = originalCameraPosition + (randomOffset * dampening);
            
            yield return null;
        }
        
        mainCamera.transform.position = originalCameraPosition;
    }

    // Lighting Effects
    private void StartLightingCycle()
    {
        if (lightingCoroutine != null)
        {
            StopCoroutine(lightingCoroutine);
        }
        lightingCoroutine = StartCoroutine(LightingCycleCoroutine());
    }

    private IEnumerator LightingCycleCoroutine()
    {
        float time = 0f;
        
        while (lightingOverlay != null)
        {
            time += Time.deltaTime * lightingCycleSpeed;
            float normalizedTime = (Mathf.Sin(time) + 1f) / 2f; // Convert to 0-1 range
            
            Color currentColor = lightingGradient.Evaluate(normalizedTime);
            lightingOverlay.style.backgroundColor = currentColor;
            
            yield return null;
        }
    }

    // Character Effects
    public void HighlightCharacter(VisualElement character, float intensity = 1.2f, float duration = 0.3f)
    {
        if (character != null)
        {
            StartCoroutine(HighlightCoroutine(character, intensity, duration));
        }
    }

    private IEnumerator HighlightCoroutine(VisualElement character, float intensity, float duration)
    {
        float originalScale = 1f;
        float elapsedTime = 0f;
        
        // Scale up
        while (elapsedTime < duration / 2f)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / (duration / 2f);
            float scale = Mathf.Lerp(originalScale, intensity, progress);
            
            character.style.scale = new StyleScale(new Scale(Vector2.one * scale));
            yield return null;
        }
        
        elapsedTime = 0f;
        
        // Scale down
        while (elapsedTime < duration / 2f)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / (duration / 2f);
            float scale = Mathf.Lerp(intensity, originalScale, progress);
            
            character.style.scale = new StyleScale(new Scale(Vector2.one * scale));
            yield return null;
        }
        
        character.style.scale = new StyleScale(new Scale(Vector2.one * originalScale));
    }

    // Background Transition
    public void TransitionBackground(Texture2D newBackground, float duration = 1f)
    {
        if (backgroundImage != null)
        {
            StartCoroutine(BackgroundTransitionCoroutine(newBackground, duration));
        }
    }

    private IEnumerator BackgroundTransitionCoroutine(Texture2D newBackground, float duration)
    {
        // Create temporary overlay for smooth transition
        var transitionOverlay = new VisualElement();
        transitionOverlay.style.position = Position.Absolute;
        transitionOverlay.style.width = new StyleLength(new Length(100, LengthUnit.Percent));
        transitionOverlay.style.height = new StyleLength(new Length(100, LengthUnit.Percent));
        transitionOverlay.style.backgroundImage = new StyleBackground(newBackground);
        transitionOverlay.style.opacity = 0f;
        
        backgroundImage.parent.Insert(backgroundImage.parent.IndexOf(backgroundImage) + 1, transitionOverlay);
        
        // Fade in new background
        float elapsedTime = 0f;
        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float alpha = elapsedTime / duration;
            transitionOverlay.style.opacity = alpha;
            yield return null;
        }
        
        // Replace original background
        backgroundImage.style.backgroundImage = new StyleBackground(newBackground);
        backgroundImage.parent.Remove(transitionOverlay);
    }

    private void OnDisable()
    {
        if (fadeCoroutine != null)
        {
            StopCoroutine(fadeCoroutine);
        }
        
        if (shakeCoroutine != null)
        {
            StopCoroutine(shakeCoroutine);
        }
        
        if (lightingCoroutine != null)
        {
            StopCoroutine(lightingCoroutine);
        }
    }
}

// Extension method for Vector2 conversion
public static class TranslateExtensions
{
    public static Vector2 ToVector2(this Translate translate)
    {
        return new Vector2(translate.x.value, translate.y.value);
    }
}


