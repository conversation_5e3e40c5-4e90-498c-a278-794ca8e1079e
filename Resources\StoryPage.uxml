<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <Style src="project://database/Assets/UI/StoryPage.uss" />
    
    <ui:VisualElement name="story-root" class="story-root">
        <ui:VisualElement name="background-image" class="background-image" />
        
        <ui:VisualElement name="character-image" class="character-image" />
        
        <ui:VisualElement name="dialog-container" class="dialog-container">
            <ui:VisualElement name="name-bar" class="name-bar">
                <ui:Label name="character-name" text="Character" class="character-name" />
            </ui:VisualElement>
            <ui:Label name="dialogue-text" text="Dialogue text here..." class="dialogue-text" />
            <ui:Button name="next-button" text="❯" class="next-button" />
        </ui:VisualElement>
        
        <ui:VisualElement name="choices-container" class="choices-container" />
        <ui:VisualElement name="name-input-container" class="name-input-container" />
    </ui:VisualElement>
</ui:UXML>