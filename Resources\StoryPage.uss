.story-root {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.background-image {
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-color: rgb(20, 30, 40);
}

.character-image {
    position: absolute;
    bottom: 0;
    right: 10%;
    width: 400px;
    height: 600px;
    background-size: contain;
    background-position: bottom;
    background-repeat: no-repeat;
}

.dialog-container {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 20px;
    min-height: 120px;
}

.name-bar {
    background-color: rgba(100, 50, 150, 0.9);
    padding: 8px 15px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.character-name {
    color: white;
    font-size: 16px;
    font-weight: bold;
}

.dialogue-text {
    color: white;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 10px;
    white-space: normal;
}

.next-button {
    position: absolute;
    bottom: 10px;
    right: 15px;
    width: 30px;
    height: 30px;
    background-color: transparent;
    border-width: 0;
    color: white;
    font-size: 18px;
}

.choices-container {
    position: absolute;
    bottom: 200px;
    left: 50%;
    translate: -50% 0;
    flex-direction: column;
    gap: 10px;
    display: none;
}

.choice-button {
    background-color: rgba(50, 100, 200, 0.9);
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    border-width: 0;
    font-size: 14px;
    min-width: 300px;
    transition-duration: 0.2s;
}

.choice-button:hover {
    background-color: rgba(70, 120, 220, 1);
    scale: 1.02;
}

.name-input-container {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    background-color: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 10px;
    flex-direction: column;
    gap: 15px;
    display: none;
}

.name-input {
    min-width: 250px;
    padding: 10px;
    font-size: 16px;
}

.submit-button {
    background-color: rgb(50, 150, 50);
    color: white;
    padding: 12px;
    border-radius: 5px;
    border-width: 0;
}

/* Control Buttons */
.controls-container {
    position: absolute;
    top: 20px;
    right: 20px;
    flex-direction: row;
    gap: 10px;
}

.control-button {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    border-width: 1px;
    border-color: rgba(255, 255, 255, 0.3);
    font-size: 12px;
    font-weight: bold;
    transition-duration: 0.2s;
    min-width: 60px;
}

.control-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    scale: 1.05;
}

.control-button.active {
    background-color: rgba(100, 150, 255, 0.8);
    border-color: rgba(100, 150, 255, 1);
}

/* Menu Overlay */
.menu-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
}

.menu-panel {
    background-color: rgba(20, 20, 30, 0.95);
    border-radius: 15px;
    padding: 30px;
    gap: 15px;
    border-width: 2px;
    border-color: rgba(100, 150, 255, 0.5);
    min-width: 250px;
}

.menu-item-button {
    background-color: rgba(50, 50, 70, 0.8);
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    border-width: 1px;
    border-color: rgba(255, 255, 255, 0.2);
    font-size: 14px;
    transition-duration: 0.2s;
}

.menu-item-button:hover {
    background-color: rgba(100, 150, 255, 0.8);
    border-color: rgba(100, 150, 255, 1);
    scale: 1.02;
}

/* Premium Effects */
.dialog-container {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.85) 0%, rgba(20, 20, 40, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.name-bar {
    background: linear-gradient(135deg, rgba(100, 50, 150, 0.9) 0%, rgba(150, 100, 200, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(100, 50, 150, 0.3);
}

.character-name {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.dialogue-text {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

.next-button:hover {
    color: rgba(255, 255, 255, 0.8);
    scale: 1.1;
}

/* Fade animations */
.fade-in {
    animation-name: fadeIn;
    animation-duration: 1s;
    animation-timing-function: ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        translate: 0 20px;
    }
    to {
        opacity: 1;
        translate: 0 0;
    }
}