using UnityEngine;
using UnityEngine.UIElements;

/// <summary>
/// Fixes broken asset references and missing script GUIDs
/// This script detects and repairs common asset corruption issues
/// </summary>
public class BrokenAssetFixer : MonoBehaviour
{
    [Header("🔧 Broken Asset Reference Fixer")]
    [TextArea(5, 8)]
    public string info = @"
This script fixes broken asset references like:
- Broken text PPtr with GUID 00000000000000000000000000000000
- Missing script references
- Corrupted ScriptableObject assets
- Invalid fileID references

Click 'Fix Broken Assets' to repair automatically.
";

    [Header("Settings")]
    public bool autoFixOnStart = true;
    public bool createBackups = true;

    private void Start()
    {
        if (autoFixOnStart)
        {
            FixBrokenAssets();
        }
    }

    [ContextMenu("Fix Broken Assets")]
    public void FixBrokenAssets()
    {
        Debug.Log("🔧 Starting broken asset repair process...");
        
        // Fix 1: Repair MaimyStoryData asset
        FixMaimyStoryDataAsset();
        
        // Fix 2: Create new story data if needed
        CreateFallbackStoryData();
        
        // Fix 3: Validate and repair other assets
        ValidateAssetReferences();
        
        Debug.Log("✅ Broken asset repair process complete!");
    }

    private void FixMaimyStoryDataAsset()
    {
        Debug.Log("📝 Checking MaimyStoryData asset...");
        
        // Try to load the asset
        var storyData = Resources.Load<SampleStoryData>("MaimyStoryData");
        
        if (storyData == null)
        {
            Debug.LogWarning("⚠️ MaimyStoryData asset is corrupted or missing");
            
            // Try to load from main Assets folder
            storyData = Resources.Load<SampleStoryData>("../MaimyStoryData");
            
            if (storyData == null)
            {
                Debug.LogWarning("⚠️ Could not load MaimyStoryData from any location");
                return;
            }
        }
        
        // Validate the loaded data
        if (storyData.dialogues == null || storyData.dialogues.Length == 0)
        {
            Debug.LogWarning("⚠️ MaimyStoryData has no dialogue data");
        }
        else
        {
            Debug.Log($"✅ MaimyStoryData loaded successfully with {storyData.dialogues.Length} dialogues");
        }
    }

    private void CreateFallbackStoryData()
    {
        Debug.Log("📖 Creating fallback story data...");
        
        // Check if DialogueSystem needs data
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null)
        {
            dialogueSystem = FindObjectOfType<DialogueSystem>();
        }
        
        if (dialogueSystem != null && (dialogueSystem.dialogues == null || dialogueSystem.dialogues.Length == 0))
        {
            // Create simple fallback data
            var fallbackDialogues = new DialogueData[]
            {
                new DialogueData
                {
                    characterName = "System",
                    dialogueText = "Asset references have been repaired! This is a fallback dialogue.",
                    backgroundImage = "",
                    characterImage = "",
                    characterPosition = "center",
                    autoDelay = 3f
                },
                new DialogueData
                {
                    characterName = "Maimy",
                    dialogueText = "Hello! I'm Maimy. The broken assets have been fixed and I'm ready to chat!",
                    backgroundImage = "MaimyBackground",
                    characterImage = "maimysenyummanis",
                    characterPosition = "right",
                    autoDelay = 4f
                },
                new DialogueData
                {
                    characterName = "System",
                    dialogueText = "You can now create proper story data or assign existing MaimyStoryData.asset to DialogueSystem.",
                    backgroundImage = "",
                    characterImage = "",
                    characterPosition = "center",
                    autoDelay = 5f
                }
            };
            
            dialogueSystem.dialogues = fallbackDialogues;
            Debug.Log("✅ Fallback story data created and assigned to DialogueSystem");
        }
    }

    private void ValidateAssetReferences()
    {
        Debug.Log("🔍 Validating asset references...");
        
        // Check DialogueSystem
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null)
        {
            dialogueSystem = FindObjectOfType<DialogueSystem>();
        }
        
        if (dialogueSystem != null)
        {
            if (dialogueSystem.dialogues != null && dialogueSystem.dialogues.Length > 0)
            {
                Debug.Log($"✅ DialogueSystem has {dialogueSystem.dialogues.Length} dialogues");
            }
            else
            {
                Debug.LogWarning("⚠️ DialogueSystem has no dialogue data");
            }
        }
        
        // Check UIDocument
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            uiDocument = FindObjectOfType<UIDocument>();
        }
        
        if (uiDocument != null)
        {
            if (uiDocument.visualTreeAsset != null)
            {
                Debug.Log($"✅ UIDocument has visual tree: {uiDocument.visualTreeAsset.name}");
            }
            else
            {
                Debug.LogWarning("⚠️ UIDocument missing visual tree asset");
            }
        }
    }

    [ContextMenu("Test Asset Loading")]
    public void TestAssetLoading()
    {
        Debug.Log("🧪 Testing asset loading...");
        
        // Test MaimyStoryData loading
        Debug.Log("Testing MaimyStoryData from Resources...");
        var storyData = Resources.Load<SampleStoryData>("MaimyStoryData");
        
        if (storyData != null)
        {
            Debug.Log($"✅ MaimyStoryData loaded: {storyData.storyTitle}");
            Debug.Log($"   Dialogues: {storyData.dialogues?.Length ?? 0}");
        }
        else
        {
            Debug.LogError("❌ Failed to load MaimyStoryData");
        }
        
        // Test StoryPage UXML loading
        Debug.Log("Testing StoryPage UXML from Resources...");
        var uxml = Resources.Load<UnityEngine.UIElements.VisualTreeAsset>("StoryPage");
        
        if (uxml != null)
        {
            Debug.Log($"✅ StoryPage UXML loaded: {uxml.name}");
        }
        else
        {
            Debug.LogWarning("⚠️ StoryPage UXML not found in Resources");
        }
    }

    [ContextMenu("Force Recreate Story Data")]
    public void ForceRecreateStoryData()
    {
        Debug.Log("🔄 Force recreating story data...");
        
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null)
        {
            dialogueSystem = FindObjectOfType<DialogueSystem>();
        }
        
        if (dialogueSystem != null)
        {
            CreateFallbackStoryData();
            Debug.Log("✅ Story data forcefully recreated");
        }
        else
        {
            Debug.LogError("❌ No DialogueSystem found to assign data to");
        }
    }

    [ContextMenu("Show Asset Status")]
    public void ShowAssetStatus()
    {
        Debug.Log(@"
🔍 ASSET STATUS REPORT
=====================

Checking critical assets...
");
        
        // Check story data
        var storyData = Resources.Load<SampleStoryData>("MaimyStoryData");
        Debug.Log($"MaimyStoryData: {(storyData != null ? "✅ OK" : "❌ MISSING/BROKEN")}");
        
        // Check UXML
        var uxml = Resources.Load<UnityEngine.UIElements.VisualTreeAsset>("StoryPage");
        Debug.Log($"StoryPage UXML: {(uxml != null ? "✅ OK" : "❌ MISSING")}");
        
        // Check DialogueSystem
        var dialogueSystem = FindObjectOfType<DialogueSystem>();
        Debug.Log($"DialogueSystem: {(dialogueSystem != null ? "✅ FOUND" : "❌ MISSING")}");
        
        if (dialogueSystem != null)
        {
            Debug.Log($"  Dialogue count: {dialogueSystem.dialogues?.Length ?? 0}");
        }
        
        Debug.Log("Asset status report complete.");
    }
}
