using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;

[System.Serializable]
public class DialogueData
{
    public string characterName;
    [TextArea(3, 10)]
    public string dialogueText;
    public string backgroundImage;
    public string characterImage;
    public string characterPosition = "center";
    public float autoDelay = 3f;
    public string audioClip;
    public bool isChoice = false;
    public string[] choices;
    public int[] choiceTargets;
}

public class DialogueSystem : MonoBehaviour
{
    [Header("Dialogue Data")]
    public DialogueData[] dialogues;
    
    [Header("Settings")]
    public float textSpeed = 0.05f;
    public bool autoMode = false;
    
    private UIDocument uiDocument;
    private VisualElement root;
    private Label dialogueText;
    private Label characterName;
    private Button nextButton;
    private VisualElement choicesContainer;
    private VisualElement backgroundImage;
    private VisualElement characterImage;
    
    private int currentDialogueIndex = 0;
    private bool isTyping = false;
    private Coroutine typingCoroutine;
    private Coroutine autoCoroutine;

    private void OnEnable()
    {
        InitializeUI();
    }

    private void InitializeUI()
    {
        uiDocument = GetComponent<UIDocument>();
        if (uiDocument != null)
        {
            root = uiDocument.rootVisualElement;
            dialogueText = root.Q<Label>("dialogue-text");
            characterName = root.Q<Label>("character-name");
            nextButton = root.Q<Button>("next-button");
            choicesContainer = root.Q("choices-container");
            backgroundImage = root.Q("background-image");
            characterImage = root.Q("character-image");

            if (nextButton != null)
            {
                nextButton.clicked += OnNextButtonClicked;
            }
        }
    }

    public void StartDialogue()
    {
        if (dialogues == null || dialogues.Length == 0)
        {
            Debug.LogError("No dialogue data available! Please assign dialogue data to start the story.");
            return;
        }

        currentDialogueIndex = 0;
        ShowDialogue(dialogues[0]);
    }

    public void OnNextButtonClicked()
    {
        if (dialogues == null || dialogues.Length == 0)
        {
            Debug.LogError("No dialogue data available!");
            return;
        }

        if (currentDialogueIndex < 0 || currentDialogueIndex >= dialogues.Length)
        {
            Debug.LogError($"Dialogue index {currentDialogueIndex} is out of range! Max index: {dialogues.Length - 1}");
            currentDialogueIndex = 0; // Reset to start
            return;
        }

        if (isTyping)
        {
            // Skip typing animation
            if (typingCoroutine != null)
            {
                StopCoroutine(typingCoroutine);
                isTyping = false;
                if (currentDialogueIndex < dialogues.Length)
                    dialogueText.text = dialogues[currentDialogueIndex].dialogueText;
            }
        }
        else
        {
            NextDialogue();
        }
    }

    private void NextDialogue()
    {
        if (dialogues == null || dialogues.Length == 0)
        {
            Debug.LogError("No dialogue data available!");
            return;
        }

        currentDialogueIndex++;
        if (currentDialogueIndex >= 0 && currentDialogueIndex < dialogues.Length)
        {
            ShowDialogue(dialogues[currentDialogueIndex]);
        }
        else
        {
            Debug.Log("Story completed!");
            currentDialogueIndex = dialogues.Length - 1; // Keep at last valid index
        }
    }

    private void ShowDialogue(DialogueData dialogue)
    {
        if (dialogue == null)
        {
            Debug.LogError("Dialogue data is null!");
            return;
        }

        if (characterName != null)
            characterName.text = dialogue.characterName ?? "";

        if (dialogueText != null)
        {
            if (typingCoroutine != null)
                StopCoroutine(typingCoroutine);

            typingCoroutine = StartCoroutine(TypeText(dialogue.dialogueText ?? ""));
        }

        LoadBackgroundImage(dialogue.backgroundImage);
        LoadCharacterImage(dialogue.characterImage);
    }

    private IEnumerator TypeText(string text)
    {
        isTyping = true;
        dialogueText.text = "";
        
        foreach (char c in text)
        {
            dialogueText.text += c;
            yield return new WaitForSeconds(textSpeed);
        }
        
        isTyping = false;
        
        if (autoMode)
        {
            autoCoroutine = StartCoroutine(AutoAdvance());
        }
    }

    private IEnumerator AutoAdvance()
    {
        if (dialogues != null && currentDialogueIndex < dialogues.Length)
        {
            yield return new WaitForSeconds(dialogues[currentDialogueIndex].autoDelay);
            NextDialogue();
        }
    }

    public void ToggleAutoMode()
    {
        autoMode = !autoMode;
        Debug.Log($"Auto mode: {(autoMode ? "ON" : "OFF")}");
    }

    private void LoadBackgroundImage(string imageName)
    {
        if (backgroundImage == null || string.IsNullOrEmpty(imageName))
            return;

        // Try to load from Resources/Background folder
        Texture2D texture = Resources.Load<Texture2D>($"Background/{imageName}");
        if (texture != null)
        {
            backgroundImage.style.backgroundImage = new StyleBackground(texture);
            Debug.Log($"Loaded background image: {imageName}");
        }
        else
        {
            Debug.LogWarning($"Background image not found: Background/{imageName}");
        }
    }

    private void LoadCharacterImage(string imageName)
    {
        if (characterImage == null || string.IsNullOrEmpty(imageName))
        {
            // Hide character image if no name provided
            if (characterImage != null)
                characterImage.style.backgroundImage = StyleKeyword.None;
            return;
        }

        // Try to load from Resources/characters/Maimy folder
        Texture2D texture = Resources.Load<Texture2D>($"characters/Maimy/{imageName}");
        if (texture != null)
        {
            characterImage.style.backgroundImage = new StyleBackground(texture);
            Debug.Log($"Loaded character image: {imageName}");
        }
        else
        {
            Debug.LogWarning($"Character image not found: characters/Maimy/{imageName}");
        }
    }
}




